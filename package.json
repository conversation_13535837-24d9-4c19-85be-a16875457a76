{"name": "huanbei-dashboard", "version": "1.0.0", "description": "A Vue2 + ElementUI2 dashboard project", "main": "index.js", "scripts": {"serve": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open", "build:analyze": "webpack --mode production && echo 'Build complete! Run: npx webpack-bundle-analyzer dist/bundle.js'", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "format": "prettier --write \"src/**/*.{js,vue,css,scss}\"", "format:check": "prettier --check \"src/**/*.{js,vue,css,scss}\"", "clean": "rm -rf dist/", "audit:security": "npm audit --audit-level moderate"}, "dependencies": {"axios": "^1.11.0", "element-ui": "^2.15.14", "vue": "^2.6.14", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.17.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.5", "css-loader": "^7.1.2", "dotenv-webpack": "^8.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^7.20.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^4.5.2", "prettier": "^2.8.0", "process": "^0.11.10", "sass": "^1.89.2", "sass-loader": "^10.5.2", "style-loader": "^1.3.0", "terser-webpack-plugin": "^4.2.3", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.6.14", "webpack": "^5.101.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}