<template>
  <el-card class="env-info-card">
    <div slot="header" class="clearfix">
      <span>环境信息</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="refreshConfig">刷新</el-button>
    </div>
    
    <div class="env-section">
      <h4>当前环境</h4>
      <el-tag :type="envTagType">{{ currentEnv }}</el-tag>
    </div>
    
    <div class="env-section">
      <h4>环境变量</h4>
      <el-table :data="envVars" size="mini" border>
        <el-table-column prop="key" label="变量名" width="200"></el-table-column>
        <el-table-column prop="value" label="值"></el-table-column>
      </el-table>
    </div>
    
    <div class="env-section">
      <h4>运行时配置</h4>
      <el-table :data="runtimeConfig" size="mini" border>
        <el-table-column prop="key" label="配置项" width="200"></el-table-column>
        <el-table-column prop="value" label="值"></el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'EnvInfo',
  data() {
    return {
      currentEnv: process.env.NODE_ENV || 'development',
      envVars: [],
      runtimeConfig: []
    }
  },
  computed: {
    envTagType() {
      const envTypes = {
        development: 'success',
        test: 'warning',
        production: 'danger'
      }
      return envTypes[this.currentEnv] || 'info'
    }
  },
  mounted() {
    this.loadEnvInfo()
  },
  methods: {
    loadEnvInfo() {
      // 加载环境变量
      this.envVars = [
        { key: 'NODE_ENV', value: process.env.NODE_ENV },
        { key: 'VUE_APP_TITLE', value: process.env.VUE_APP_TITLE },
        { key: 'VUE_APP_VERSION', value: process.env.VUE_APP_VERSION },
        { key: 'VUE_APP_API_URL', value: process.env.VUE_APP_API_URL },
        { key: 'VUE_APP_API_TIMEOUT', value: process.env.VUE_APP_API_TIMEOUT },
        { key: 'VUE_APP_BASE_URL', value: process.env.VUE_APP_BASE_URL },
        { key: 'VUE_APP_ENABLE_MOCK', value: process.env.VUE_APP_ENABLE_MOCK },
        { key: 'VUE_APP_ENABLE_DEBUG', value: process.env.VUE_APP_ENABLE_DEBUG },
        { key: 'VUE_APP_LOG_LEVEL', value: process.env.VUE_APP_LOG_LEVEL },
        { key: 'VUE_APP_UPLOAD_URL', value: process.env.VUE_APP_UPLOAD_URL },
        { key: 'VUE_APP_WEBSOCKET_URL', value: process.env.VUE_APP_WEBSOCKET_URL },
        { key: 'VUE_APP_ENABLE_PWA', value: process.env.VUE_APP_ENABLE_PWA },
        { key: 'VUE_APP_ENABLE_ANALYTICS', value: process.env.VUE_APP_ENABLE_ANALYTICS }
      ].filter(item => item.value !== undefined)

      // 加载运行时配置
      const config = window.getConfig ? window.getConfig() : {}
      this.runtimeConfig = Object.keys(config).map(key => ({
        key,
        value: typeof config[key] === 'object' ? JSON.stringify(config[key]) : String(config[key])
      }))
    },
    refreshConfig() {
      this.loadEnvInfo()
      this.$message.success('配置信息已刷新')
    }
  }
}
</script>

<style lang="scss" scoped>
.env-info-card {
  margin-bottom: 20px;
}

.env-section {
  margin-bottom: 20px;

  h4 {
    margin-bottom: 10px;
    color: #606266;
    font-size: 14px;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}

.clearfix:after {
  clear: both;
}
</style>
