<template>
  <el-card class="stat-card">
    <div class="stat-content">
      <div class="stat-icon" :class="iconClass">
        <i :class="icon"></i>
      </div>
      <div class="stat-info">
        <div class="stat-number">{{ formattedValue }}</div>
        <div class="stat-label">{{ label }}</div>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    value: {
      type: [Number, String],
      required: true
    },
    label: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    iconClass: {
      type: String,
      default: ''
    },
    format: {
      type: String,
      default: 'number' // 'number', 'currency', 'percent'
    }
  },
  computed: {
    formattedValue() {
      if (this.format === 'currency') {
        return `¥${Number(this.value).toLocaleString()}`
      } else if (this.format === 'percent') {
        return `${this.value}%`
      }
      return Number(this.value).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$card-height: 120px;
$icon-size: 60px;
$transition-duration: 0.3s;
$white: white;
$text-color: #2c3e50;
$label-color: #7f8c8d;

// 渐变色定义
$user-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$order-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$revenue-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$growth-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

.stat-card {
  height: $card-height;
  cursor: pointer;
  transition: all $transition-duration ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: $icon-size;
  height: $icon-size;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: $white;

  &.user-icon {
    background: $user-gradient;
  }

  &.order-icon {
    background: $order-gradient;
  }

  &.revenue-icon {
    background: $revenue-gradient;
  }

  &.growth-icon {
    background: $growth-gradient;
  }
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: $text-color;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: $label-color;
}
</style>
