<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <div slot="header" class="clearfix">
            <span>欢迎使用环贝仪表板</span>
          </div>
          <div class="welcome-content">
            <el-alert
              title="系统状态正常"
              type="success"
              :closable="false"
              show-icon
            >
            </el-alert>
            <p class="welcome-text">
              这是一个基于 Vue2 + ElementUI2 构建的现代化仪表板应用。
              您可以通过导航菜单访问不同的功能模块。
            </p>
            <div class="quick-actions">
              <el-button type="primary" @click="$router.push('/dashboard')">
                <i class="el-icon-data-line"></i>
                查看仪表板
              </el-button>
              <el-button type="info" @click="$router.push('/about')">
                <i class="el-icon-info"></i>
                了解更多
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="8">
        <el-card>
          <div slot="header">
            <i class="el-icon-user"></i>
            用户信息
          </div>
          <div class="info-item">
            <span class="label">用户名：</span>
            <span>{{ userInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">角色：</span>
            <el-tag type="success">{{ userInfo.role }}</el-tag>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <div slot="header">
            <i class="el-icon-time"></i>
            系统时间
          </div>
          <div class="time-display">
            {{ currentTime }}
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <div slot="header">
            <i class="el-icon-bell"></i>
            通知
          </div>
          <el-badge :value="3" class="notification-badge">
            <el-button size="small">消息</el-button>
          </el-badge>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Home',
  data() {
    return {
      currentTime: ''
    }
  },
  computed: {
    ...mapGetters(['getUserInfo']),
    userInfo() {
      return this.getUserInfo
    }
  },
  mounted() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    /**
     * 更新当前时间
     */
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409eff;
$text-color: #666;
$content-padding: 20px;
$button-margin: 10px;
$label-min-width: 60px;

.welcome-card {
  text-align: center;
}

.welcome-content {
  padding: $content-padding;
}

.welcome-text {
  margin: 20px 0;
  font-size: 16px;
  color: $text-color;
  line-height: 1.6;
}

.quick-actions {
  margin-top: 30px;

  .el-button {
    margin: 0 $button-margin;
  }
}

.info-item {
  margin: $button-margin 0;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: $button-margin;
  min-width: $label-min-width;
}

.time-display {
  font-size: 18px;
  font-weight: bold;
  color: $primary-color;
  text-align: center;
}

.notification-badge {
  display: block;
  text-align: center;
}
</style>
