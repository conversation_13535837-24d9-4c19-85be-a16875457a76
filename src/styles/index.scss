// 全局样式入口文件

// 导入变量和 mixins
@import './variables.scss';
@import './mixins.scss';

// 全局重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: PingFangSC, PingFang SC;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $background-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: $transition-base;
  
  &:hover {
    color: darken($primary-color, 10%);
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 $spacing-md 0;
  font-weight: 600;
  line-height: 1.4;
  color: $text-color-primary;
}

h1 { font-size: $font-size-xxl; }
h2 { font-size: $font-size-xl; }
h3 { font-size: $font-size-lg; }
h4 { font-size: $font-size-base; }
h5 { font-size: $font-size-sm; }
h6 { font-size: $font-size-xs; }

// 段落样式
p {
  margin: 0 0 $spacing-md 0;
  line-height: 1.6;
  color: $text-color-regular;
}

// 列表样式
ul, ol {
  margin: 0 0 $spacing-md 0;
  padding-left: $spacing-lg;
  
  li {
    margin-bottom: $spacing-xs;
    line-height: 1.6;
    color: $text-color-regular;
  }
}

// 表格样式
table {
  @include table-style;
}

// 表单元素样式
input,
textarea,
select {
  @include input-style;
}

// 按钮基础样式
.btn {
  display: inline-block;
  padding: $spacing-sm $spacing-md;
  border: 1px solid transparent;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  font-weight: 400;
  text-align: center;
  cursor: pointer;
  transition: $transition-base;
  user-select: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 按钮变体
.btn-primary {
  @include button-variant($white, $primary-color, $primary-color);
}

.btn-success {
  @include button-variant($white, $success-color, $success-color);
}

.btn-warning {
  @include button-variant($white, $warning-color, $warning-color);
}

.btn-danger {
  @include button-variant($white, $danger-color, $danger-color);
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-info { color: $info-color; }

.bg-primary { background-color: $primary-color; }
.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-danger { background-color: $danger-color; }
.bg-info { background-color: $info-color; }

// 间距工具类
@for $i from 0 through 5 {
  .m-#{$i} { margin: #{$i * $spacing-xs}; }
  .mt-#{$i} { margin-top: #{$i * $spacing-xs}; }
  .mr-#{$i} { margin-right: #{$i * $spacing-xs}; }
  .mb-#{$i} { margin-bottom: #{$i * $spacing-xs}; }
  .ml-#{$i} { margin-left: #{$i * $spacing-xs}; }
  
  .p-#{$i} { padding: #{$i * $spacing-xs}; }
  .pt-#{$i} { padding-top: #{$i * $spacing-xs}; }
  .pr-#{$i} { padding-right: #{$i * $spacing-xs}; }
  .pb-#{$i} { padding-bottom: #{$i * $spacing-xs}; }
  .pl-#{$i} { padding-left: #{$i * $spacing-xs}; }
}

// Flex 工具类
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

// 显示/隐藏工具类
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

// 响应式显示/隐藏
@include respond-to(xs) {
  .d-xs-none { display: none; }
  .d-xs-block { display: block; }
}

@include respond-to(sm) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
}

@include respond-to(md) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
}

@include respond-to(lg) {
  .d-lg-none { display: none; }
  .d-lg-block { display: block; }
}

// 滚动条样式
.custom-scrollbar {
  @include scrollbar-style;
}

// 卡片样式
.card {
  @include card-style;
}

// 加载动画
.loading {
  @include loading-spinner;
}

// 文本省略号
.text-ellipsis {
  @include text-ellipsis;
}

.text-ellipsis-2 {
  @include text-ellipsis-multiline(2);
}

.text-ellipsis-3 {
  @include text-ellipsis-multiline(3);
}
