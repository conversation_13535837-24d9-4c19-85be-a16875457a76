# 环北仪表板 (Huanbei Dashboard)

一个基于 Vue 2 + Element UI 的现代化仪表板项目，提供数据可视化、用户管理和系统监控功能。

## 🚀 特性

- **现代化技术栈**: Vue 2.6 + Element UI 2.15 + Vuex + Vue Router
- **响应式设计**: 支持桌面端和移动端
- **模块化架构**: 清晰的项目结构，易于维护和扩展
- **完整的HTTP工具**: 封装的 Axios 请求库，支持拦截器、错误处理、Loading状态
- **代码规范**: ESLint + Prettier 确保代码质量
- **环境配置**: 支持多环境配置（开发/测试/生产）

## 📦 技术栈

- **前端框架**: Vue 2.6.14
- **UI组件库**: Element UI 2.15.14
- **状态管理**: Vuex 3.6.2
- **路由管理**: Vue Router 3.6.5
- **HTTP客户端**: Axios 1.11.0
- **构建工具**: Webpack 4.46.0
- **代码规范**: ESLint 7.32.0 + Prettier 2.8.0

## 🛠️ 开发环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0

## 📥 安装

```bash
# 克隆项目
git clone <repository-url>
cd huanbei-dashboard

# 安装依赖
npm install
```

## 🚦 使用

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 或者
npm run serve
```

访问 http://localhost:8080

### 生产构建

```bash
# 构建生产版本
npm run build
```

### 代码检查和格式化

```bash
# 运行 ESLint 检查
npm run lint

# 自动修复 ESLint 错误
npm run lint:fix

# 格式化代码
npm run format

# 检查代码格式
npm run format:check
```

## 📁 项目结构

```
src/
├── api/                 # API 接口管理
│   └── index.js        # 统一的 API 接口定义
├── components/         # 通用组件
│   ├── ApiExample.vue  # API 使用示例组件
│   └── StatCard.vue    # 统计卡片组件
├── router/             # 路由配置
│   └── index.js        # 路由定义
├── store/              # Vuex 状态管理
│   └── index.js        # 状态管理配置
├── utils/              # 工具函数
│   └── request.js      # HTTP 请求封装
├── views/              # 页面组件
│   ├── About.vue       # 关于页面
│   ├── Dashboard.vue   # 仪表板页面
│   └── Home.vue        # 首页
├── App.vue             # 根组件
└── main.js             # 应用入口
```

## 🔧 配置

### 环境配置

项目支持多环境配置，配置文件位于：
- `public/config.js` - 运行时配置，支持开发、测试、生产环境
- Webpack DefinePlugin - 编译时环境变量注入

### API 配置

HTTP 请求配置在 `src/utils/request.js` 中，支持：
- 请求/响应拦截器
- 自动 Loading 状态管理
- 统一错误处理
- Token 自动添加
- 请求超时配置

API 接口定义在 `src/api/index.js` 中，支持：
- 按模块分类
- 封装统一的请求方法
- 自动添加前缀
