<template>
  <div class="dashboard">
    <div class="header">
      <img class="logo" src="../assets/images/icons/icon.png" alt="">
      <span class="title">环北部湾广东水资源配置工程 — BIM管理平台</span>
      <div class="tab">
        <div class="item" :class="isProjectSelected ? 'selected': ''" @click="handleClickTab">
          <span>项目一览</span>
          <img src="../assets/images/common/selected.png" alt="" v-if="isProjectSelected">
        </div>
        <div class="item" :class="isProjectSelected ? '': 'selected'" @click="handleClickTab">
          <span>模型平台</span>
          <img src="../assets/images/common/selected.png" alt="" v-if="!isProjectSelected">
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Dashboard',
  components: {
  },
  data() {
    return {
      isProjectSelected: true
    }
  },
  computed: {
    ...mapGetters(['getDashboardData']),
    dashboardData() {
      return this.getDashboardData
    }
  },
  methods: {
    handleClickTab(){
      this.isProjectSelected = !this.isProjectSelected
    },
    ...mapActions(['fetchDashboardData']),
    refreshData() {
      this.$message({
        message: '正在刷新数据...',
        type: 'info'
      })
      this.fetchDashboardData()
      setTimeout(() => {
        this.$message({
          message: '数据刷新成功！',
          type: 'success'
        })
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$icon-size: 60px;
$transition-duration: 0.3s;
$white: white;
$text-color: #2c3e50;
$label-color: #7f8c8d;
$button-min-width: 120px;

.dashboard{
  background: #2c3e50;
  height: 100vh;
  width: 100%;
  .header{
    display: flex;
    align-items: center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 46px;
    background-image: url("../assets/images/background/bg.png");
    .logo{
      width: 30px;
      height: 30px;
      margin-left: 20px;
    }
    .title{
      margin-left: 7px;
      font-weight: 500;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 33px;
      text-align: left;
      font-style: normal;
    }
    .tab{
      margin-left: 100px;
      display: flex;
      flex: 1;
      height: 100%;
      .item{
        margin-top: 17px;
        position: relative;
        margin-right: 60px;
        cursor: pointer;
        span{
          margin-top: 60px;
          font-size: 16px;
          color: white;
        }
        &.selected{
          color: #19FFFF;
        }
        img{
          position: absolute;
          left: -18px;
          bottom: 0;
          width: 104px;
          height: 14px;
        }
      }
    }
  }
}

</style>
