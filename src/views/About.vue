<template>
  <div class="about">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <div slot="header">
            <span>关于环贝仪表板</span>
          </div>
          <div class="about-content">
            <el-descriptions title="项目信息" :column="2" border>
              <el-descriptions-item label="项目名称"
                >环贝仪表板</el-descriptions-item
              >
              <el-descriptions-item label="版本号">v1.0.0</el-descriptions-item>
              <el-descriptions-item label="开发框架"
                >Vue 2.6.14</el-descriptions-item
              >
              <el-descriptions-item label="UI组件库"
                >Element UI 2.15.14</el-descriptions-item
              >
              <el-descriptions-item label="状态管理"
                >Vuex 3.6.2</el-descriptions-item
              >
              <el-descriptions-item label="路由管理"
                >Vue Router 3.6.5</el-descriptions-item
              >
              <el-descriptions-item label="构建工具"
                >Vue CLI 5.0</el-descriptions-item
              >
              <el-descriptions-item label="开发语言"
                >JavaScript</el-descriptions-item
              >
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <i class="el-icon-cpu"></i>
            技术特性
          </div>
          <ul class="feature-list">
            <li>
              <el-tag type="success">响应式设计</el-tag>
              <span>适配各种屏幕尺寸</span>
            </li>
            <li>
              <el-tag type="primary">组件化开发</el-tag>
              <span>模块化的组件架构</span>
            </li>
            <li>
              <el-tag type="warning">状态管理</el-tag>
              <span>集中式的状态管理</span>
            </li>
            <li>
              <el-tag type="info">路由导航</el-tag>
              <span>单页面应用路由</span>
            </li>
            <li>
              <el-tag type="danger">现代化UI</el-tag>
              <span>美观的用户界面</span>
            </li>
          </ul>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <div slot="header">
            <i class="el-icon-info"></i>
            系统信息
          </div>
          <div class="system-info">
            <div class="info-row">
              <span class="info-label">浏览器：</span>
              <span>{{ browserInfo }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">屏幕分辨率：</span>
              <span>{{ screenResolution }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">当前时间：</span>
              <span>{{ currentTime }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">运行状态：</span>
              <el-tag type="success" size="small">正常运行</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-card>
          <div slot="header">
            <i class="el-icon-document"></i>
            项目说明
          </div>
          <div class="project-description">
            <p>
              环贝仪表板是一个基于 Vue2 和 Element UI 2
              构建的现代化管理后台系统。
              该项目采用了最新的前端技术栈，提供了完整的项目结构和开发规范。
            </p>
            <p>项目特点：</p>
            <ul>
              <li>🎨 现代化的UI设计，基于Element UI组件库</li>
              <li>📱 响应式布局，支持多种设备访问</li>
              <li>🔧 完整的开发工具链配置</li>
              <li>📊 丰富的数据展示组件</li>
              <li>🚀 高性能的单页面应用架构</li>
              <li>🛡️ 类型安全和代码规范检查</li>
            </ul>
            <div class="action-buttons">
              <el-button type="primary" @click="goToDashboard">
                <i class="el-icon-data-line"></i>
                查看仪表板
              </el-button>
              <el-button type="success" @click="goToHome">
                <i class="el-icon-house"></i>
                返回首页
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 环境信息 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <EnvInfo />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import EnvInfo from '@/components/EnvInfo.vue'

export default {
  name: 'About',
  components: {
    EnvInfo
  },
  data() {
    return {
      currentTime: '',
      browserInfo: '',
      screenResolution: ''
    }
  },
  mounted() {
    this.updateTime()
    this.getBrowserInfo()
    this.getScreenResolution()
    this.timer = setInterval(this.updateTime, 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
    },
    getBrowserInfo() {
      const ua = navigator.userAgent
      if (ua.indexOf('Chrome') > -1) {
        this.browserInfo = 'Google Chrome'
      } else if (ua.indexOf('Firefox') > -1) {
        this.browserInfo = 'Mozilla Firefox'
      } else if (ua.indexOf('Safari') > -1) {
        this.browserInfo = 'Safari'
      } else if (ua.indexOf('Edge') > -1) {
        this.browserInfo = 'Microsoft Edge'
      } else {
        this.browserInfo = '未知浏览器'
      }
    },
    getScreenResolution() {
      this.screenResolution = `${screen.width} x ${screen.height}`
    },
    goToDashboard() {
      this.$router.push('/dashboard')
    },
    goToHome() {
      this.$router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$content-padding: 20px;
$item-margin: 15px;
$border-color: #ebeef5;
$text-color: #606266;
$background-color: #f8f9fa;
$border-radius: 4px;
$button-margin: 10px;
$tag-min-width: 80px;
$label-min-width: 100px;

.about-content {
  padding: $content-padding 0;
}

.feature-list {
  list-style: none;
  padding: 0;

  li {
    display: flex;
    align-items: center;
    margin: $item-margin 0;
    padding: $button-margin;
    background-color: $background-color;
    border-radius: $border-radius;

    .el-tag {
      margin-right: $item-margin;
      min-width: $tag-min-width;
    }
  }
}

.system-info {
  padding: $button-margin 0;
}

.info-row {
  display: flex;
  align-items: center;
  margin: $item-margin 0;
  padding: 8px 0;
  border-bottom: 1px solid $border-color;
}

.info-label {
  font-weight: bold;
  min-width: $label-min-width;
  color: $text-color;
}

.project-description {
  padding: $content-padding 0;
  line-height: 1.8;

  p {
    margin: $item-margin 0;
    color: $text-color;
  }

  ul {
    margin: $content-padding 0;
    padding-left: $content-padding;
  }

  li {
    margin: $button-margin 0;
    color: $text-color;
  }
}

.action-buttons {
  margin-top: 30px;
  text-align: center;

  .el-button {
    margin: 0 $button-margin;
  }
}
</style>
