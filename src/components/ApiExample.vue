<template>
  <div class="api-example">
    <h2>Axios工具类使用示例</h2>

    <!-- 用户信息展示 -->
    <el-card class="box-card" style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>用户信息</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="getUserInfo"
          >刷新</el-button
        >
      </div>
      <div v-if="userInfo">
        <p>用户名: {{ userInfo.username }}</p>
        <p>邮箱: {{ userInfo.email }}</p>
        <p>角色: {{ userInfo.role }}</p>
      </div>
      <div v-else>
        <p>暂无用户信息</p>
      </div>
    </el-card>

    <!-- 数据列表 -->
    <el-card class="box-card" style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>数据列表</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="getDataList"
          >加载数据</el-button
        >
      </div>
      <el-table :data="dataList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="status" label="状态"></el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button size="mini" @click="editData(scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="deleteData(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 文件上传 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>文件上传示例</span>
      </div>
      <el-upload
        class="upload-demo"
        :before-upload="beforeUpload"
        :http-request="customUpload"
        :show-file-list="false"
        action="#"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">
          只能上传jpg/png文件，且不超过500kb
        </div>
      </el-upload>

      <div
        v-if="uploadProgress > 0 && uploadProgress < 100"
        style="margin-top: 10px"
      >
        <el-progress :percentage="uploadProgress"></el-progress>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ApiExample',
  data() {
    return {
      userInfo: null,
      dataList: [],
      uploadProgress: 0
    }
  },

  mounted() {
    // 组件挂载后获取用户信息
    this.getUserInfo()
  },

  methods: {
    // 获取用户信息 - 使用API封装
    async getUserInfo() {
      try {
        const response = await this.$api.userApi.getUserInfo()
        this.userInfo = response.data
        this.$message.success('用户信息获取成功')
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 错误已在拦截器中处理，这里可以做额外处理
      }
    },

    // 获取数据列表 - 使用直接请求方式
    async getDataList() {
      try {
        const response = await this.$request.get('/data/list', {
          page: 1,
          size: 10
        })
        this.dataList = response.data.list
        this.$message.success('数据加载成功')
      } catch (error) {
        console.error('获取数据列表失败:', error)
      }
    },

    // 编辑数据
    async editData(row) {
      try {
        await this.$api.dataApi.updateData(row.id, {
          name: row.name + '_edited',
          status: 'updated'
        })
        this.$message.success('数据更新成功')
        this.getDataList() // 刷新列表
      } catch (error) {
        console.error('更新数据失败:', error)
      }
    },

    // 删除数据
    async deleteData(id) {
      try {
        await this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await this.$api.dataApi.deleteData(id)
        this.$message.success('删除成功')
        this.getDataList() // 刷新列表
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除数据失败:', error)
        }
      }
    },

    // 文件上传前的检查
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt500K = file.size / 1024 < 500

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt500K) {
        this.$message.error('上传头像图片大小不能超过 500KB!')
      }
      return isJPG && isLt500K
    },

    // 自定义上传方法
    async customUpload(options) {
      const formData = new FormData()
      formData.append('file', options.file)

      try {
        this.uploadProgress = 0

        const response = await this.$api.fileApi.uploadFile(formData, {
          onProgress: progress => {
            this.uploadProgress = progress
          }
        })

        this.$message.success('文件上传成功')
        this.uploadProgress = 0
        console.log('上传结果:', response)
      } catch (error) {
        this.uploadProgress = 0
        console.error('文件上传失败:', error)
      }
    },

    // 下载文件示例
    async downloadFile() {
      try {
        await this.$api.fileApi.downloadFile('file123', 'example.xlsx')
        this.$message.success('文件下载成功')
      } catch (error) {
        console.error('文件下载失败:', error)
      }
    },

    // 批量操作示例
    async batchDelete() {
      const selectedIds = [1, 2, 3] // 假设选中的ID
      try {
        await this.$api.dataApi.batchDeleteData(selectedIds)
        this.$message.success('批量删除成功')
        this.getDataList()
      } catch (error) {
        console.error('批量删除失败:', error)
      }
    },

    // 无loading请求示例
    async getDataWithoutLoading() {
      try {
        const response = await this.$request.get(
          '/data/list',
          {},
          {
            loading: false // 不显示loading
          }
        )
        console.log('数据:', response.data)
      } catch (error) {
        console.error('请求失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$content-padding: 20px;
$card-margin: 20px;
$upload-margin: 10px;

.api-example {
  padding: $content-padding;
}

.box-card {
  margin-bottom: $card-margin;
}

// 清除浮动的 mixin
%clearfix {
  &:before,
  &:after {
    display: table;
    content: '';
  }

  &:after {
    clear: both;
  }
}

.clearfix {
  @extend %clearfix;
}

.upload-demo {
  margin-top: $upload-margin;
}
</style>
