import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import request from '@/utils/request'
import api from '@/api'

// 引入全局样式
import '@/styles/index.scss'

Vue.config.productionTip = false

// 使用ElementUI
Vue.use(ElementUI)

// 将axios工具类挂载到Vue原型上
Vue.prototype.$request = request
Vue.prototype.$api = api

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
