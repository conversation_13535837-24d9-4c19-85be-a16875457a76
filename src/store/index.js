import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    loading: false,
    error: null,
    user: {
      name: '管理员',
      role: 'admin'
    },
    dashboardData: {
      totalUsers: 1250,
      totalOrders: 3680,
      totalRevenue: 125000,
      growthRate: 12.5
    }
  },
  getters: {
    getUserInfo: state => state.user,
    getDashboardData: state => state.dashboardData,
    isLoading: state => state.loading,
    getError: state => state.error
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    SET_ERROR(state, error) {
      state.error = error
    },
    CLEAR_ERROR(state) {
      state.error = null
    },
    SET_USER_INFO(state, userInfo) {
      state.user = userInfo
    },
    UPDATE_DASHBOARD_DATA(state, data) {
      state.dashboardData = { ...state.dashboardData, ...data }
    }
  },
  actions: {
    async updateUserInfo({ commit }, userInfo) {
      try {
        commit('SET_LOADING', true)
        commit('CLEAR_ERROR')
        // 这里可以添加API调用
        commit('SET_USER_INFO', userInfo)
      } catch (error) {
        commit('SET_ERROR', error.message || '更新用户信息失败')
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    },
    async fetchDashboardData({ commit }) {
      try {
        commit('SET_LOADING', true)
        commit('CLEAR_ERROR')

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        const mockData = {
          totalUsers: Math.floor(Math.random() * 2000) + 1000,
          totalOrders: Math.floor(Math.random() * 5000) + 3000,
          totalRevenue: Math.floor(Math.random() * 200000) + 100000,
          growthRate: (Math.random() * 20 + 5).toFixed(1)
        }

        commit('UPDATE_DASHBOARD_DATA', mockData)
      } catch (error) {
        commit('SET_ERROR', error.message || '获取仪表板数据失败')
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    }
  },
  modules: {}
})
