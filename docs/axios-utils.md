# Axios 工具类使用文档

## 概述

本项目封装了一个功能完整的 Axios 工具类，提供了统一的 HTTP 请求管理、错误处理、loading 状态管理、拦截器配置等功能。

## 文件结构

```
src/
├── utils/
│   └── request.js          # Axios 工具类核心文件
├── api/
│   └── index.js            # API 接口统一管理
├── components/
│   └── ApiExample.vue      # 使用示例组件
public/
└── config.js               # 环境配置文件
```

## 核心功能

### 1. 请求拦截器
- 自动添加 Authorization token
- 显示全局 loading 状态
- GET 请求自动添加时间戳防缓存
- 统一请求头配置

### 2. 响应拦截器
- 统一错误处理和提示
- 自动处理 401 未授权跳转
- 隐藏 loading 状态
- 业务状态码处理

### 3. Loading 管理
- 全局 loading 状态控制
- 支持多个并发请求的 loading 计数
- 可配置是否显示 loading

### 4. 错误处理
- HTTP 状态码错误处理
- 网络错误处理
- 超时错误处理
- 业务错误处理

## 使用方法

### 1. 基础使用

在 Vue 组件中可以通过以下方式使用：

```javascript
// 方式一：使用封装的 API
this.$api.userApi.getUserInfo()

// 方式二：直接使用 request 工具
this.$request.get('/user/info')
```

### 2. GET 请求

```javascript
// 基础 GET 请求
const response = await this.$request.get('/api/users')

// 带参数的 GET 请求
const response = await this.$request.get('/api/users', {
  page: 1,
  size: 10,
  keyword: 'search'
})

// 不显示 loading 的请求
const response = await this.$request.get('/api/users', {}, {
  loading: false
})
```

### 3. POST 请求

```javascript
// 基础 POST 请求
const response = await this.$request.post('/api/users', {
  name: 'John',
  email: '<EMAIL>'
})

// 使用 API 封装
const response = await this.$api.userApi.login({
  username: 'admin',
  password: '123456'
})
```

### 4. 文件上传

```javascript
// 创建 FormData
const formData = new FormData()
formData.append('file', file)

// 上传文件（带进度回调）
const response = await this.$api.fileApi.uploadFile(formData, {
  onProgress: (progress) => {
    console.log(`上传进度: ${progress}%`)
  }
})

// 或者直接使用 upload 方法
const response = await this.$request.upload('/api/upload', formData)
```

### 5. 文件下载

```javascript
// 下载文件
await this.$api.fileApi.downloadFile('fileId', 'filename.xlsx')

// 或者直接使用 download 方法
const response = await this.$request.download('/api/download', {
  fileId: 'xxx'
})
```

### 6. 其他 HTTP 方法

```javascript
// PUT 请求
await this.$request.put('/api/users/1', { name: 'Updated Name' })

// DELETE 请求
await this.$request.delete('/api/users/1')

// PATCH 请求
await this.$request.patch('/api/users/1', { status: 'active' })
```

## API 管理

### 模块化 API 管理

在 `src/api/index.js` 中，API 按功能模块进行分类：

```javascript
// 用户相关 API
export const userApi = {
  login(data) {
    return request.post('/user/login', data)
  },
  getUserInfo() {
    return request.get('/user/info')
  }
}

// 数据管理 API
export const dataApi = {
  getDataList(params) {
    return request.get('/data/list', params)
  },
  createData(data) {
    return request.post('/data', data)
  }
}
```

### 使用 API 模块

```javascript
// 在组件中使用
export default {
  methods: {
    async loadUserInfo() {
      try {
        const response = await this.$api.userApi.getUserInfo()
        this.userInfo = response.data
      } catch (error) {
        // 错误已在拦截器中处理
        console.error('获取用户信息失败:', error)
      }
    }
  }
}
```

## 环境配置

### 配置文件

在 `public/config.js` 中配置不同环境的 API 地址：

```javascript
window.CONFIG = {
  development: {
    baseURL: 'http://localhost:3000/api',
    timeout: 15000,
    debug: true
  },
  production: {
    baseURL: '/api',
    timeout: 15000,
    debug: false
  }
}
```

### 获取配置

```javascript
const config = window.getConfig()
console.log('当前环境配置:', config)
```

## 错误处理

### 全局错误处理

工具类已经内置了全局错误处理，包括：

- HTTP 状态码错误（400, 401, 403, 404, 500 等）
- 网络连接错误
- 请求超时错误
- 业务逻辑错误

### 自定义错误处理

```javascript
try {
  const response = await this.$request.get('/api/data')
  // 处理成功响应
} catch (error) {
  // 自定义错误处理
  if (error.response?.status === 404) {
    this.$message.error('数据不存在')
  }
}
```

## 高级功能

### 1. 请求取消

```javascript
import axios from 'axios'

const source = axios.CancelToken.source()

const response = await this.$request.get('/api/data', {}, {
  cancelToken: source.token
})

// 取消请求
source.cancel('请求被取消')
```

### 2. 请求重试

```javascript
// 在 request.js 中可以添加重试逻辑
service.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config
    if (!config.retry) config.retry = 0
    
    if (config.retry < 3) {
      config.retry++
      return service(config)
    }
    
    return Promise.reject(error)
  }
)
```

### 3. 请求缓存

```javascript
// 可以添加请求缓存功能
const cache = new Map()

service.interceptors.request.use(config => {
  if (config.method === 'get' && config.cache) {
    const key = config.url + JSON.stringify(config.params)
    if (cache.has(key)) {
      return Promise.resolve(cache.get(key))
    }
  }
  return config
})
```

## 最佳实践

### 1. API 接口命名规范

```javascript
// 好的命名
getUserList()     // 获取用户列表
createUser()      // 创建用户
updateUser()      // 更新用户
deleteUser()      // 删除用户

// 避免的命名
getUsers()        // 不够明确
user()           // 过于简单
handleUser()     // 不明确操作
```

### 2. 错误处理最佳实践

```javascript
// 推荐的错误处理方式
async loadData() {
  try {
    this.loading = true
    const response = await this.$api.dataApi.getDataList()
    this.dataList = response.data
  } catch (error) {
    // 全局错误已处理，这里只需要处理特殊逻辑
    this.dataList = []
  } finally {
    this.loading = false
  }
}
```

### 3. 性能优化

```javascript
// 避免重复请求
let requestPromise = null

async getData() {
  if (requestPromise) {
    return requestPromise
  }
  
  requestPromise = this.$api.dataApi.getData()
  try {
    return await requestPromise
  } finally {
    requestPromise = null
  }
}
```

## 常见问题

### Q1: 如何禁用某个请求的 loading？

```javascript
const response = await this.$request.get('/api/data', {}, {
  loading: false
})
```

### Q2: 如何处理文件下载？

```javascript
// 使用封装的下载方法
await this.$api.fileApi.downloadFile('fileId', 'filename.xlsx')

// 或者手动处理
const response = await this.$request.download('/api/file/download')
const blob = new Blob([response.data])
const url = window.URL.createObjectURL(blob)
// ... 创建下载链接
```

### Q3: 如何添加自定义请求头？

```javascript
const response = await this.$request.get('/api/data', {}, {
  headers: {
    'Custom-Header': 'value'
  }
})
```

### Q4: 如何处理大文件上传？

```javascript
const formData = new FormData()
formData.append('file', file)

const response = await this.$request.upload('/api/upload', formData, {
  timeout: 300000, // 5分钟超时
  onUploadProgress: (progressEvent) => {
    const progress = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total
    )
    console.log(`上传进度: ${progress}%`)
  }
})
```

## 总结

这个 Axios 工具类提供了完整的 HTTP 请求解决方案，包括：

- ✅ 统一的请求/响应处理
- ✅ 全局 loading 状态管理
- ✅ 完善的错误处理机制
- ✅ 文件上传/下载支持
- ✅ 环境配置管理
- ✅ API 模块化管理
- ✅ TypeScript 友好（可扩展）

通过使用这个工具类，可以大大简化项目中的 HTTP 请求处理，提高开发效率和代码质量。