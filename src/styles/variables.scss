// 全局 Sass 变量文件

// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

$white: #ffffff;
$black: #000000;

// 文本颜色
$text-color-primary: #2c3e50;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 背景颜色
$background-color-base: #f5f7fa;
$background-color-light: #f8f9fa;
$background-color-lighter: #fafafa;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 尺寸变量
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 6px;

// 间距变量
$spacing-xs: 5px;
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 24px;
$font-size-xxl: 28px;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);

// 过渡动画
$transition-base: all 0.3s ease;
$transition-fast: all 0.2s ease;
$transition-slow: all 0.5s ease;

// 布局变量
$header-height: 60px;
$sidebar-width: 200px;
$content-padding: 20px;

// 组件特定变量
$card-height: 120px;
$icon-size: 60px;
$button-min-width: 120px;
$label-min-width: 100px;

// 渐变色定义
$gradient-user: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-order: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-revenue: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-growth: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// Z-index 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
