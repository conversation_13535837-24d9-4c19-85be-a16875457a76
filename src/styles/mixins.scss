// 全局 Sass mixins 文件

// 清除浮动
@mixin clearfix {
  &:before,
  &:after {
    display: table;
    content: '';
  }
  
  &:after {
    clear: both;
  }
}

// 文本省略号
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略号
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Flex 布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// 按钮样式
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover,
  &:focus {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 12%);
    border-color: darken($border, 12%);
  }
}

// 卡片样式
@mixin card-style($padding: $spacing-lg) {
  background: $white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  padding: $padding;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $box-shadow-hover;
  }
}

// 渐变背景
@mixin gradient-background($gradient) {
  background: $gradient;
  background-size: 100% 100%;
}

// 图标样式
@mixin icon-style($size: $icon-size, $color: $white) {
  width: $size;
  height: $size;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $size * 0.4;
  color: $color;
}

// 输入框样式
@mixin input-style {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  transition: $transition-base;
  
  &:focus {
    border-color: $primary-color;
    outline: none;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
  
  &::placeholder {
    color: $text-color-placeholder;
  }
}

// 表格样式
@mixin table-style {
  width: 100%;
  border-collapse: collapse;
  
  th,
  td {
    padding: $spacing-sm $spacing-md;
    text-align: left;
    border-bottom: 1px solid $border-color-lighter;
  }
  
  th {
    background-color: $background-color-light;
    font-weight: 600;
    color: $text-color-primary;
  }
  
  tr:hover {
    background-color: $background-color-lighter;
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 2px solid rgba($color, 0.3);
  border-top: 2px solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 滚动条样式
@mixin scrollbar-style($width: 6px, $track-color: $background-color-light, $thumb-color: $border-color-base) {
  &::-webkit-scrollbar {
    width: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
